<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Management System</title>
    
    <!-- External Libraries -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/motion@10.16.2/dist/motion.js"></script>
    <script src="https://unpkg.com/@lottiefiles/lottie-player@latest/dist/lottie-player.js"></script>
    <script src="https://cdn.jsdelivr.net/gh/studio-freight/lenis@1.0.19/bundled/lenis.min.js"></script>

    <style>
        :root {
            --primary-color: #6366f1;
            --secondary-color: #f8fafc;
            --accent-color: #fbbf24;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --border-color: #e5e7eb;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --border-radius: 12px;
            --spacing-xs: 0.25rem;
            --spacing-sm: 0.5rem;
            --spacing-md: 1rem;
            --spacing-lg: 1.5rem;
            --spacing-xl: 2rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: var(--text-primary);
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: var(--spacing-xl);
        }

        .header {
            text-align: center;
            margin-bottom: var(--spacing-xl);
            color: white;
        }

        .header h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: var(--spacing-sm);
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .controls {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            align-items: center;
            justify-content: center;
        }

        .search-box {
            flex: 1;
            min-width: 300px;
            max-width: 400px;
            position: relative;
        }

        .search-box input {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius);
            font-size: 1rem;
            background: white;
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
        }

        .search-box input:focus {
            outline: none;
            box-shadow: var(--shadow-lg);
            transform: translateY(-2px);
        }

        .search-box i {
            position: absolute;
            right: var(--spacing-md);
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-secondary);
        }

        .btn {
            padding: var(--spacing-md) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius);
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-sm);
            text-decoration: none;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
            box-shadow: var(--shadow);
        }

        .btn-primary:hover {
            background: #5855eb;
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .btn-secondary {
            background: white;
            color: var(--text-primary);
            box-shadow: var(--shadow);
        }

        .btn-secondary:hover {
            background: #f9fafb;
            transform: translateY(-2px);
        }

        .btn-secondary.active {
            background: var(--primary-color);
            color: white;
        }

        .column-controls {
            display: flex;
            gap: var(--spacing-sm);
        }

        .filter-section {
            background: white;
            padding: var(--spacing-lg);
            border-radius: var(--border-radius);
            margin-bottom: var(--spacing-xl);
            box-shadow: var(--shadow);
        }

        .filter-tags {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-sm);
            margin-top: var(--spacing-md);
        }

        .filter-tag {
            padding: var(--spacing-sm) var(--spacing-md);
            background: #f3f4f6;
            border: 2px solid transparent;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .filter-tag:hover {
            background: #e5e7eb;
        }

        .filter-tag.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .products-grid {
            display: grid;
            gap: var(--spacing-lg);
            grid-template-columns: repeat(5, 1fr);
            transition: all 0.3s ease;
        }

        /* Responsive Grid */
        @media (max-width: 600px) {
            .products-grid { grid-template-columns: repeat(2, 1fr); }
        }
        @media (min-width: 601px) and (max-width: 900px) {
            .products-grid { grid-template-columns: repeat(3, 1fr); }
        }
        @media (min-width: 901px) and (max-width: 1200px) {
            .products-grid { grid-template-columns: repeat(4, 1fr); }
        }

        .product-card {
            background: white;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
            cursor: pointer;
            opacity: 0;
            transform: translateY(20px);
        }

        .product-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-lg);
        }

        .product-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
            background: #f3f4f6;
        }

        .product-content {
            padding: var(--spacing-lg);
        }

        .product-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: var(--spacing-sm);
            line-height: 1.4;
        }

        .product-description {
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin-bottom: var(--spacing-md);
            line-height: 1.5;
        }

        .product-rating {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-md);
        }

        .stars {
            color: var(--accent-color);
        }

        .product-tags {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-xs);
        }

        .tag {
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 2px;
        }

        .tag-forever {
            background: #FFF9C4;
            color: #92400e;
        }

        .tag-company {
            background: #FCE4EC;
            color: #be185d;
        }

        .tag-body {
            background: #ddd6fe;
            color: #6b21a8;
        }

        .tag-manual {
            background: #f3f4f6;
            color: var(--text-secondary);
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            backdrop-filter: blur(4px);
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: var(--border-radius);
            padding: var(--spacing-xl);
            max-width: 600px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: var(--shadow-lg);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-lg);
        }

        .modal-header h2 {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--text-secondary);
            padding: var(--spacing-sm);
        }

        .form-group {
            margin-bottom: var(--spacing-lg);
        }

        .form-group label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 500;
            color: var(--text-primary);
        }

        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: var(--spacing-md);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        .checkbox-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: var(--spacing-sm);
            margin-top: var(--spacing-sm);
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .modal-actions {
            display: flex;
            gap: var(--spacing-md);
            justify-content: flex-end;
            margin-top: var(--spacing-xl);
        }

        .empty-state {
            text-align: center;
            padding: var(--spacing-xl);
            color: var(--text-secondary);
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: var(--spacing-lg);
            opacity: 0.5;
        }

        @media (max-width: 768px) {
            .container {
                padding: var(--spacing-md);
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .controls {
                flex-direction: column;
                align-items: stretch;
            }
            
            .search-box {
                min-width: auto;
                max-width: none;
            }
            
            .column-controls {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1><i class="fas fa-box-open"></i> Product Manager</h1>
            <p>Manage your products with style and efficiency</p>
        </header>

        <div class="controls">
            <div class="search-box">
                <input type="text" id="searchInput" placeholder="Search products...">
                <i class="fas fa-search"></i>
            </div>
            
            <button class="btn btn-primary" id="addProductBtn">
                <lottie-player src="https://assets2.lottiefiles.com/packages/lf20_V9t630.json" 
                              background="transparent" speed="1" style="width: 20px; height: 20px;" 
                              loop autoplay></lottie-player>
                Add Product
            </button>
            
            <div class="column-controls">
                <button class="btn btn-secondary" data-columns="2">2 Col</button>
                <button class="btn btn-secondary" data-columns="3">3 Col</button>
                <button class="btn btn-secondary" data-columns="4">4 Col</button>
                <button class="btn btn-secondary active" data-columns="5">5 Col</button>
            </div>
        </div>

        <div class="filter-section">
            <h3><i class="fas fa-filter"></i> Filter by Tags</h3>
            <div class="filter-tags" id="filterTags"></div>
            <button class="btn btn-secondary" id="clearFilters" style="margin-top: 1rem;">
                <i class="fas fa-times"></i> Clear Filters
            </button>
        </div>

        <div class="products-grid" id="productsGrid">
            <div class="empty-state">
                <i class="fas fa-box"></i>
                <h3>No products yet</h3>
                <p>Click "Add Product" to get started!</p>
            </div>
        </div>
    </div>

    <!-- Add Product Modal -->
    <div class="modal" id="addProductModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2><i class="fas fa-plus-circle"></i> Add New Product</h2>
                <button class="close-btn" id="closeModal">&times;</button>
            </div>
            
            <form id="productForm">
                <div class="form-group">
                    <label for="productTitle">Product Title *</label>
                    <input type="text" id="productTitle" required>
                </div>
                
                <div class="form-group">
                    <label for="productImage">Product Image URL *</label>
                    <input type="url" id="productImage" required>
                </div>
                
                <div class="form-group">
                    <label for="productDescription">Short Benefit Description</label>
                    <textarea id="productDescription" maxlength="200" rows="3" 
                              placeholder="Describe the key benefits..."></textarea>
                    <small>Max 200 characters</small>
                </div>
                
                <div class="form-group">
                    <label for="productRating">Star Rating (1-10)</label>
                    <input type="number" id="productRating" min="1" max="10" value="5">
                </div>
                
                <div class="form-group">
                    <label for="manualTags">Manual Tags (comma-separated)</label>
                    <input type="text" id="manualTags" placeholder="tag1, tag2, tag3">
                </div>
                
                <div class="form-group">
                    <label for="companyTag">Company Tag</label>
                    <input type="text" id="companyTag" placeholder="Company name">
                </div>
                
                <div class="form-group">
                    <label>Body Area Tags</label>
                    <div class="checkbox-group" id="bodyAreaTags"></div>
                </div>
                
                <div class="modal-actions">
                    <button type="button" class="btn btn-secondary" id="cancelBtn">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Save Product
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Product Detail Modal -->
    <div class="modal" id="productDetailModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="detailTitle"></h2>
                <button class="close-btn" id="closeDetailModal">&times;</button>
            </div>
            <div id="detailContent"></div>
        </div>
    </div>

    <script>
        // Initialize Lenis for smooth scrolling
        const lenis = new Lenis({
            duration: 1.2,
            easing: (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t)),
            direction: 'vertical',
            gestureDirection: 'vertical',
            smooth: true,
            mouseMultiplier: 1,
            smoothTouch: false,
            touchMultiplier: 2,
            infinite: false,
        });

        function raf(time) {
            lenis.raf(time);
            requestAnimationFrame(raf);
        }
        requestAnimationFrame(raf);

        // Application State
        let products = JSON.parse(localStorage.getItem('products')) || [];
        let currentColumns = 5;
        let activeFilters = [];
        let searchQuery = '';

        // Body area tags
        const bodyAreas = [
            'arms', 'face', 'lips', 'hair', 'nose', 'undereyes', 'neck', 'chest', 
            'full body', 'forehead', 'legs', 'private parts', 'elbow', 'knee', 'underarms'
        ];

        // Initialize app
        document.addEventListener('DOMContentLoaded', function() {
            initializeBodyAreaCheckboxes();
            initializeEventListeners();
            renderProducts();
            updateFilterTags();
        });

        function initializeBodyAreaCheckboxes() {
            const container = document.getElementById('bodyAreaTags');
            bodyAreas.forEach(area => {
                const div = document.createElement('div');
                div.className = 'checkbox-item';
                div.innerHTML = `
                    <input type="checkbox" id="body-${area}" value="${area}">
                    <label for="body-${area}">${area}</label>
                `;
                container.appendChild(div);
            });
        }

        function initializeEventListeners() {
            // Modal controls
            document.getElementById('addProductBtn').addEventListener('click', openAddModal);
            document.getElementById('closeModal').addEventListener('click', closeAddModal);
            document.getElementById('closeDetailModal').addEventListener('click', closeDetailModal);
            document.getElementById('cancelBtn').addEventListener('click', closeAddModal);
            
            // Form submission
            document.getElementById('productForm').addEventListener('submit', handleFormSubmit);
            
            // Search
            const searchInput = document.getElementById('searchInput');
            let searchTimeout;
            searchInput.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    searchQuery = e.target.value.toLowerCase();
                    renderProducts();
                }, 300);
            });
            
            // Column controls
            document.querySelectorAll('[data-columns]').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    const columns = parseInt(e.target.dataset.columns);
                    setColumns(columns);
                });
            });
            
            // Filter controls
            document.getElementById('clearFilters').addEventListener('click', clearFilters);
            
            // Close modals on backdrop click
            document.getElementById('addProductModal').addEventListener('click', (e) => {
                if (e.target.id === 'addProductModal') closeAddModal();
            });
            
            document.getElementById('productDetailModal').addEventListener('click', (e) => {
                if (e.target.id === 'productDetailModal') closeDetailModal();
            });
        }

        function openAddModal() {
            const modal = document.getElementById('addProductModal');
            modal.style.display = 'block';
            
            // Animate modal entrance
            const content = modal.querySelector('.modal-content');
            Motion.animate(content, 
                { opacity: [0, 1], scale: [0.8, 1] }, 
                { duration: 0.3, easing: 'ease-out' }
            );
        }

        function closeAddModal() {
            const modal = document.getElementById('addProductModal');
            const content = modal.querySelector('.modal-content');
            
            Motion.animate(content, 
                { opacity: [1, 0], scale: [1, 0.8] }, 
                { duration: 0.2, easing: 'ease-in' }
            ).finished.then(() => {
                modal.style.display = 'none';
                document.getElementById('productForm').reset();
            });
        }

        function closeDetailModal() {
            const modal = document.getElementById('productDetailModal');
            const content = modal.querySelector('.modal-content');
            
            Motion.animate(content, 
                { opacity: [1, 0], scale: [1, 0.8] }, 
                { duration: 0.2, easing: 'ease-in' }
            ).finished.then(() => {
                modal.style.display = 'none';
            });
        }

        function handleFormSubmit(e) {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const selectedBodyAreas = Array.from(document.querySelectorAll('#bodyAreaTags input:checked'))
                .map(cb => cb.value);
            
            const manualTags = document.getElementById('manualTags').value
                .split(',')
                .map(tag => tag.trim())
                .filter(tag => tag.length > 0);
            
            const product = {
                id: Date.now().toString(),
                title: document.getElementById('productTitle').value,
                imageUrl: document.getElementById('productImage').value,
                description: document.getElementById('productDescription').value,
                rating: parseInt(document.getElementById('productRating').value),
                tags: {
                    forever: true,
                    company: document.getElementById('companyTag').value,
                    bodyAreas: selectedBodyAreas,
                    manual: manualTags
                },
                createdAt: new Date().toISOString()
            };
            
            products.push(product);
            localStorage.setItem('products', JSON.stringify(products));
            
            closeAddModal();
            renderProducts();
            updateFilterTags();
            
            // Show success animation
            showNotification('Product added successfully!');
        }

        function renderProducts() {
            const grid = document.getElementById('productsGrid');
            const filteredProducts = filterProducts();
            
            if (filteredProducts.length === 0) {
                grid.innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-search"></i>
                        <h3>No products found</h3>
                        <p>Try adjusting your search or filters</p>
                    </div>
                `;
                return;
            }
            
            grid.innerHTML = '';
            
            filteredProducts.forEach((product, index) => {
                const card = createProductCard(product);
                grid.appendChild(card);
                
                // Animate card entrance
                setTimeout(() => {
                    Motion.animate(card, 
                        { opacity: [0, 1], y: [20, 0] }, 
                        { duration: 0.4, easing: 'ease-out' }
                    );
                }, index * 100);
            });
        }

        function createProductCard(product) {
            const card = document.createElement('div');
            card.className = 'product-card';
            card.addEventListener('click', () => openProductDetail(product));
            
            const stars = '★'.repeat(Math.floor(product.rating)) + 
                         '☆'.repeat(10 - Math.floor(product.rating));
            
            const allTags = [];
            if (product.tags.forever) allTags.push({ text: 'Forever', type: 'forever', icon: 'fa-eagle' });
            if (product.tags.company) allTags.push({ text: product.tags.company, type: 'company' });
            product.tags.bodyAreas.forEach(area => allTags.push({ text: area, type: 'body' }));
            product.tags.manual.forEach(tag => allTags.push({ text: tag, type: 'manual' }));
            
            card.innerHTML = `
                <img src="${product.imageUrl}" alt="${product.title}" class="product-image" 
                     onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzZiNzI4MCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPk5vIEltYWdlPC90ZXh0Pjwvc3ZnPg=='">
                <div class="product-content">
                    <h3 class="product-title">${product.title}</h3>
                    <p class="product-description">${product.description}</p>
                    <div class="product-rating">
                        <span class="stars">${stars}</span>
                        <span>${product.rating}/10</span>
                    </div>
                    <div class="product-tags">
                        ${allTags.map(tag => `
                            <span class="tag tag-${tag.type}">
                                ${tag.icon ? `<i class="fas ${tag.icon}"></i>` : ''}
                                ${tag.text}
                            </span>
                        `).join('')}
                    </div>
                </div>
            `;
            
            return card;
        }

        function openProductDetail(product) {
            const modal = document.getElementById('productDetailModal');
            const title = document.getElementById('detailTitle');
            const content = document.getElementById('detailContent');
            
            title.innerHTML = `<i class="fas fa-info-circle"></i> ${product.title}`;
            
            const stars = '★'.repeat(Math.floor(product.rating)) + 
                         '☆'.repeat(10 - Math.floor(product.rating));
            
            const allTags = [];
            if (product.tags.forever) allTags.push({ text: 'Forever', type: 'forever', icon: 'fa-eagle' });
            if (product.tags.company) allTags.push({ text: product.tags.company, type: 'company' });
            product.tags.bodyAreas.forEach(area => allTags.push({ text: area, type: 'body' }));
            product.tags.manual.forEach(tag => allTags.push({ text: tag, type: 'manual' }));
            
            content.innerHTML = `
                <div style="text-align: center; margin-bottom: 2rem;">
                    <img src="${product.imageUrl}" alt="${product.title}" 
                         style="max-width: 100%; height: 300px; object-fit: cover; border-radius: 12px; box-shadow: var(--shadow);"
                         onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxOCIgZmlsbD0iIzZiNzI4MCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPk5vIEltYWdlPC90ZXh0Pjwvc3ZnPg=='">
                </div>
                <div style="margin-bottom: 1.5rem;">
                    <h4 style="margin-bottom: 0.5rem; color: var(--text-secondary);">Description</h4>
                    <p style="font-size: 1.1rem; line-height: 1.6;">${product.description || 'No description provided.'}</p>
                </div>
                <div style="margin-bottom: 1.5rem;">
                    <h4 style="margin-bottom: 0.5rem; color: var(--text-secondary);">Rating</h4>
                    <div style="display: flex; align-items: center; gap: 0.5rem; font-size: 1.2rem;">
                        <span style="color: var(--accent-color);">${stars}</span>
                        <span style="font-weight: 600;">${product.rating}/10</span>
                    </div>
                </div>
                <div>
                    <h4 style="margin-bottom: 1rem; color: var(--text-secondary);">Tags</h4>
                    <div style="display: flex; flex-wrap: wrap; gap: 0.5rem;">
                        ${allTags.map(tag => `
                            <span class="tag tag-${tag.type}" style="font-size: 0.9rem; padding: 0.5rem 1rem;">
                                ${tag.icon ? `<i class="fas ${tag.icon}"></i>` : ''}
                                ${tag.text}
                            </span>
                        `).join('')}
                    </div>
                </div>
                <div style="margin-top: 2rem; padding-top: 1.5rem; border-top: 1px solid var(--border-color); color: var(--text-secondary); font-size: 0.9rem;">
                    <i class="fas fa-calendar"></i> Added on ${new Date(product.createdAt).toLocaleDateString()}
                </div>
            `;
            
            modal.style.display = 'block';
            
            // Animate modal entrance
            const modalContent = modal.querySelector('.modal-content');
            Motion.animate(modalContent, 
                { opacity: [0, 1], scale: [0.8, 1] }, 
                { duration: 0.3, easing: 'ease-out' }
            );
        }

        function filterProducts() {
            return products.filter(product => {
                // Search filter
                if (searchQuery && !product.title.toLowerCase().includes(searchQuery)) {
                    return false;
                }
                
                // Tag filter
                if (activeFilters.length > 0) {
                    const productTags = [];
                    if (product.tags.forever) productTags.push('Forever');
                    if (product.tags.company) productTags.push(product.tags.company);
                    productTags.push(...product.tags.bodyAreas);
                    productTags.push(...product.tags.manual);
                    
                    const hasMatchingTag = activeFilters.some(filter => 
                        productTags.some(tag => tag.toLowerCase().includes(filter.toLowerCase()))
                    );
                    
                    if (!hasMatchingTag) return false;
                }
                
                return true;
            });
        }

        function updateFilterTags() {
            const container = document.getElementById('filterTags');
            const allTags = new Set();
            
            products.forEach(product => {
                if (product.tags.forever) allTags.add('Forever');
                if (product.tags.company) allTags.add(product.tags.company);
                product.tags.bodyAreas.forEach(tag => allTags.add(tag));
                product.tags.manual.forEach(tag => allTags.add(tag));
            });
            
            container.innerHTML = '';
            Array.from(allTags).sort().forEach(tag => {
                const tagElement = document.createElement('div');
                tagElement.className = 'filter-tag';
                tagElement.textContent = tag;
                tagElement.addEventListener('click', () => toggleFilter(tag, tagElement));
                container.appendChild(tagElement);
            });
        }

        function toggleFilter(tag, element) {
            const index = activeFilters.indexOf(tag);
            if (index > -1) {
                activeFilters.splice(index, 1);
                element.classList.remove('active');
            } else {
                activeFilters.push(tag);
                element.classList.add('active');
            }
            renderProducts();
        }

        function clearFilters() {
            activeFilters = [];
            document.querySelectorAll('.filter-tag').forEach(tag => {
                tag.classList.remove('active');
            });
            renderProducts();
        }

        function setColumns(columns) {
            currentColumns = columns;
            const grid = document.getElementById('productsGrid');
            grid.style.gridTemplateColumns = `repeat(${columns}, 1fr)`;
            
            // Update active button
            document.querySelectorAll('[data-columns]').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-columns="${columns}"]`).classList.add('active');
        }

        function showNotification(message) {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: var(--primary-color);
                color: white;
                padding: 1rem 1.5rem;
                border-radius: var(--border-radius);
                box-shadow: var(--shadow-lg);
                z-index: 10000;
                font-weight: 500;
            `;
            notification.innerHTML = `<i class="fas fa-check-circle"></i> ${message}`;
            
            document.body.appendChild(notification);
            
            Motion.animate(notification, 
                { opacity: [0, 1], x: [100, 0] }, 
                { duration: 0.3, easing: 'ease-out' }
            );
            
            setTimeout(() => {
                Motion.animate(notification, 
                    { opacity: [1, 0], x: [0, 100] }, 
                    { duration: 0.3, easing: 'ease-in' }
                ).finished.then(() => {
                    document.body.removeChild(notification);
                });
            }, 3000);
        }
    </script>
</body>
</html>
